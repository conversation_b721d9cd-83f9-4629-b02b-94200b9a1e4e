# 定时任务功能操作演示脚本

## 🎬 演示场景：创建一个每日新闻爬取任务

### 准备工作
1. 确保已安装依赖：`pip install APScheduler==3.10.4`
2. 启动服务：`python main.py`
3. 打开浏览器：http://127.0.0.1:5511

---

## 📝 详细操作步骤

### 第一部分：创建爬虫配置（2分钟）

**操作1：进入爬虫配置页面**
```
👆 点击页面顶部的"爬虫配置"标签
```

**操作2：设置要爬取的URL**
```
📝 在URL输入框中输入：
https://www.baidu.com/s?wd=人工智能新闻
https://www.baidu.com/s?wd=机器学习最新进展
https://www.baidu.com/s?wd=深度学习技术
```

**操作3：保存配置**
```
👆 点击"保存当前配置"按钮
📝 输入配置名称：AI技术新闻爬取
👆 点击"保存"按钮
✅ 看到"配置保存成功"提示
```

### 第二部分：创建定时任务（3分钟）

**操作4：进入定时任务页面**
```
👆 点击页面顶部的"定时任务"标签
📋 可以看到当前还没有任务（空列表）
```

**操作5：开始创建任务**
```
👆 点击右上角的"创建定时任务"按钮
📱 弹出创建任务的对话框
```

**操作6：填写基本信息**
```
📝 任务名称：每日AI技术新闻爬取
📋 爬虫配置：选择"AI技术新闻爬取"
```

**操作7：设置调度时间**
```
📋 调度类型：选择"每日执行"
🕘 小时：选择"08"（上午8点）
🕘 分钟：选择"00"（整点）
```

**操作8：配置可选项**
```
✅ 过滤重复链接：保持勾选
✅ 任务内去重：保持勾选
📋 文案过滤配置：选择"不使用文案过滤"
📋 URL过滤配置：选择"不使用URL过滤"
```

**操作9：创建任务**
```
👆 点击"创建任务"按钮
✅ 看到"定时任务创建成功"提示
📱 对话框自动关闭
```

### 第三部分：查看和管理任务（2分钟）

**操作10：查看任务列表**
```
📋 在任务列表中可以看到刚创建的任务：
   - 任务名称：每日AI技术新闻爬取
   - 配置：AI技术新闻爬取
   - 调度类型：每日执行
   - 调度配置：每天 08:00
   - 状态：启用（绿色标签）
   - 创建时间：刚才的时间
```

**操作11：测试任务管理功能**
```
👆 点击"禁用"按钮
📋 状态变为"禁用"（灰色标签）
👆 再点击"启用"按钮
📋 状态变回"启用"（绿色标签）
```

**操作12：查看执行记录**
```
👆 点击"执行记录"按钮
📋 可以看到执行记录表格（目前为空，因为还没到执行时间）
```

### 第四部分：演示不同的调度类型（3分钟）

**操作13：创建每周任务**
```
👆 再次点击"创建定时任务"
📝 任务名称：每周技术总结
📋 爬虫配置：选择"AI技术新闻爬取"
📋 调度类型：选择"每周执行"
📋 星期：选择"星期一"
🕘 小时：选择"09"
🕘 分钟：选择"00"
👆 点击"创建任务"
```

**操作14：创建间隔任务**
```
👆 再次点击"创建定时任务"
📝 任务名称：实时技术监控
📋 爬虫配置：选择"AI技术新闻爬取"
📋 调度类型：选择"间隔执行"
📝 间隔数值：输入"4"
📋 间隔类型：选择"小时"
👆 点击"创建任务"
```

**操作15：创建Cron任务**
```
👆 再次点击"创建定时任务"
📝 任务名称：自定义时间任务
📋 爬虫配置：选择"AI技术新闻爬取"
📋 调度类型：选择"自定义Cron"
📝 Cron表达式：输入"0 */6 * * *"
👆 点击"创建任务"
```

### 第五部分：查看最终结果（1分钟）

**操作16：查看所有任务**
```
📋 现在任务列表中有4个任务：
   1. 每日AI技术新闻爬取 - 每天 08:00
   2. 每周技术总结 - 每周一 09:00  
   3. 实时技术监控 - 每 4 小时
   4. 自定义时间任务 - 0 */6 * * *
```

**操作17：演示删除任务**
```
👆 点击最后一个任务的"删除"按钮
📱 弹出确认对话框："确定要删除此定时任务吗？"
👆 点击"确定"
📋 任务从列表中消失
```

---

## 🎯 演示要点总结

### ✅ 成功演示了：
1. **配置创建**：如何保存爬虫配置
2. **任务创建**：如何创建定时任务
3. **时间设置**：4种不同的调度类型
4. **任务管理**：启用、禁用、删除操作
5. **界面导航**：各个功能页面的切换

### 💡 关键演示点：
1. **简单易用**：整个过程只需点击和选择，无需编程
2. **功能完整**：支持多种时间模式，满足不同需求
3. **管理方便**：可以随时查看、修改、删除任务
4. **状态清晰**：任务状态、执行记录一目了然

### 🎬 演示时长：约11分钟
- 配置创建：2分钟
- 基础任务创建：3分钟
- 任务管理：2分钟
- 多种调度类型：3分钟
- 总结展示：1分钟

---

## 📋 演示后的说明

### 实际使用中：
1. **任务会自动执行**：到了设定时间，系统会自动运行爬虫
2. **数据自动保存**：爬取的结果会保存到数据库
3. **可以查看结果**：在"搜索结果"页面查看所有数据
4. **执行记录跟踪**：每次执行都有详细记录

### 建议用户：
1. **从简单开始**：先创建一个每日任务进行测试
2. **合理设置频率**：避免过于频繁的执行
3. **定期检查**：查看执行记录，确保任务正常
4. **根据需要调整**：可以随时修改或删除任务

这样，用户就能完全理解如何使用定时任务功能了！🎉
