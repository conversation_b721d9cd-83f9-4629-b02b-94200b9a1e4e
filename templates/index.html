<!DOCTYPE html>
<html>
    <head>
        <title>百度搜索爬虫管理系统</title>
        <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />
        <!-- 使用国内 CDN -->
        <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.1.3/css/bootstrap.min.css" rel="stylesheet" />
        <!-- Material Icons 使用阿里图标库替代 -->
        <link href="https://at.alicdn.com/t/font_8d5l8fzk5b87iudi.css" rel="stylesheet" />
        <style>
            :root {
                --primary-color: #2196f3;
                --secondary-color: #607d8b;
                --success-color: #4caf50;
                --danger-color: #f44336;
                --warning-color: #ffc107;
                --info-color: #00bcd4;
            }

            body {
                background-color: #f5f5f5;
                font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
                font-size: 14px; /* 减小基础字体大小 */
            }

            .container-fluid {
                width: 90%; /* 减小容器宽度 */
                margin: 15px auto; /* 减小外边距 */
                padding: 0;
            }

            .page-header {
                background: white;
                padding: 1rem; /* 减小内边距 */
                border-radius: 8px; /* 减小圆角 */
                margin-bottom: 1.5rem; /* 减小下边距 */
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); /* 减小阴影 */
            }

            .page-header h1 {
                margin: 0;
                color: var(--primary-color);
                font-size: 1.5rem; /* 减小标题字体 */
                font-weight: 500;
            }

            .nav-tabs {
                border: none;
                margin-bottom: 15px; /* 减小边距 */
            }

            .nav-tabs .nav-link {
                border: none;
                color: var(--secondary-color);
                padding: 0.75rem 1rem; /* 减小内边距 */
                border-radius: 6px; /* 减小圆角 */
                margin-right: 0.4rem; /* 减小间距 */
                font-size: 0.9rem; /* 减小字体 */
            }

            .nav-tabs .nav-link:hover {
                background: rgba(33, 150, 243, 0.1);
                color: var(--primary-color);
            }

            .nav-tabs .nav-link.active {
                background: var(--primary-color);
                color: white;
            }

            .card {
                border: none;
                border-radius: 8px; /* 减小圆角 */
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); /* 减小阴影 */
                margin-bottom: 15px; /* 减小边距 */
            }

            .card-body {
                padding: 1rem; /* 减小内边距 */
            }

            .form-control,
            .form-select {
                border-radius: 6px; /* 减小圆角 */
                border: 1px solid #ddd;
                padding: 0.5rem 0.75rem; /* 减小内边距 */
                font-size: 0.9rem; /* 减小字体 */
            }

            .form-control:focus,
            .form-select:focus {
                border-color: var(--primary-color);
                box-shadow: 0 0 0 0.2rem rgba(33, 150, 243, 0.25);
            }

            .btn {
                border-radius: 6px; /* 减小圆角 */
                padding: 0.5rem 1rem; /* 减小内边距 */
                font-size: 0.9rem; /* 减小字体 */
                font-weight: 500;
            }

            .btn-primary {
                background: var(--primary-color);
                border: none;
            }

            .btn-success {
                background: var(--success-color);
                border: none;
            }

            .btn-danger {
                background: var(--danger-color);
                border: none;
            }

            .btn-sm {
                padding: 0.25rem 0.5rem; /* 减小小按钮内边距 */
                font-size: 0.8rem; /* 减小小按钮字体 */
            }

            .table {
                background: white;
                border-radius: 10px;
                overflow: hidden;
                font-size: 0.9rem; /* 减小表格字体 */
            }

            .table th {
                background: #f8f9fa;
                font-weight: 500;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                padding: 0.5rem; /* 减小表格单元格内边距 */
                border-top: none;
            }

            .table td {
                padding: 0.5rem; /* 减小表格单元格内边距 */
                vertical-align: middle;
            }

            .status-badge {
                padding: 0.5rem 1rem;
                border-radius: 20px;
                font-size: 0.875rem;
                font-weight: 500;
            }

            .execution-key {
                font-family: monospace;
                background: #f8f9fa;
                padding: 0.25rem 0.5rem;
                border-radius: 4px;
            }

            .log-container {
                background: #2b2b2b;
                color: #e6e6e6;
                padding: 1rem;
                border-radius: 8px;
                font-family: monospace;
                height: 300px; /* 减小日志容器高度 */
                overflow-y: auto;
                font-size: 0.85rem; /* 减小日志字体 */
            }

            .log-entry {
                margin-bottom: 0.5rem;
                line-height: 1.5;
            }

            /* 美化复选框 */
            .form-check {
                padding-left: 2rem;
            }

            .form-check-input {
                width: 1.2rem;
                height: 1.2rem;
                margin-left: -2rem;
                border-radius: 4px;
                border: 2px solid #ddd;
            }

            .form-check-input:checked {
                background-color: var(--primary-color);
                border-color: var(--primary-color);
            }

            /* 添加动画效果 */
            .fade-enter {
                opacity: 0;
            }

            .fade-enter-active {
                opacity: 1;
                transition: opacity 300ms ease-in;
            }

            /* 响应式调整 */
            @media (max-width: 768px) {
                .container-fluid {
                    width: 100%;
                }

                .nav-tabs .nav-link {
                    padding: 0.75rem 1rem;
                }
            }

            /* 修改图标类名以适配阿里图标 */
            .iconfont {
                font-size: 16px; /* 减小图标大小 */
                margin-right: 4px; /* 减小图标右边距 */
                vertical-align: middle;
            }

            /* 减小表单组间距 */
            .mb-3 {
                margin-bottom: 0.75rem !important;
            }

            .mb-4 {
                margin-bottom: 1rem !important;
            }

            /* 减小搜索框组件尺寸 */
            .row.g-3 {
                gap: 0.75rem !important;
            }

            .url-list-container {
                background: white;
                border-radius: 6px;
                border: 1px solid #eee;
                margin-bottom: 1rem;
            }

            .search-type-badge {
                display: inline-block;
                padding: 2px 8px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: 500;
            }

            .search-type-title {
                background: #e3f2fd;
                color: #1976d2;
            }

            .search-type-site {
                background: #f3e5f5;
                color: #7b1fa2;
            }

            .search-condition {
                font-size: 13px;
                color: #666;
                max-width: 500px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .search-keywords {
                color: #2196f3;
                font-weight: 500;
            }

            /* 调整文本框和表格容器的样式 */
            #urls {
                height: 400px;
                font-size: 13px;
                font-family: monospace;
                line-height: 1.4;
            }

            .url-list-container {
                background: white;
                border-radius: 6px;
                border: 1px solid #eee;
                height: 400px;
                overflow-y: auto;
            }

            /* 调整表格样式使其更紧凑 */
            .table-sm td,
            .table-sm th {
                padding: 0.3rem;
                font-size: 13px;
            }
        </style>
    </head>
    <body>
        <div class="container-fluid">
            <div class="page-header">
                <h1><i class="iconfont icon-search"></i>百度搜索爬虫管理系统</h1>
            </div>

            <!-- 标签页导航 -->
            <ul class="nav nav-tabs" role="tablist">
                <li class="nav-item">
                    <button class="nav-link active" id="crawler-tab" data-bs-toggle="tab" data-bs-target="#crawler"><i class="iconfont icon-setting"></i>爬虫配置</button>
                </li>
                <li class="nav-item">
                    <button class="nav-link" id="configs-tab" data-bs-toggle="tab" data-bs-target="#configs"><i class="iconfont icon-list"></i>配置管理</button>
                </li>
                <li class="nav-item">
                    <button class="nav-link" id="results-tab" data-bs-toggle="tab" data-bs-target="#results"><i class="iconfont icon-file"></i>爬取结果</button>
                </li>
                <li class="nav-item">
                    <button class="nav-link" id="executions-tab" data-bs-toggle="tab" data-bs-target="#executions"><i class="iconfont icon-history"></i>爬取记录</button>
                </li>
                <li class="nav-item">
                    <button class="nav-link" id="filters-tab" data-bs-toggle="tab" data-bs-target="#filters"><i class="iconfont icon-filter"></i>文案过滤</button>
                </li>
                <li class="nav-item">
                    <button class="nav-link" id="url-filters-tab" data-bs-toggle="tab" data-bs-target="#url-filters"><i class="iconfont icon-filter"></i>URL过滤</button>
                </li>
            </ul>

            <!-- 标签页内容 -->
            <div class="tab-content">
                <!-- 爬虫配置页面 -->
                <div class="tab-pane fade show active" id="crawler">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-body">
                                    <div class="mb-4">
                                        <label class="form-label">选择配置</label>
                                        <select class="form-select" id="configSelect" onchange="loadSelectedConfig()">
                                            <option value="">请选择配置...</option>
                                        </select>
                                    </div>

                                    <div class="mb-4">
                                        <label class="form-label">选择文案过滤配置</label>
                                        <select class="form-select" id="filterConfigSelect" onchange="showFilterConfigDetails(this.value)">
                                            <option value="">不使用文案过滤</option>
                                        </select>
                                        <div id="filterConfigDetails" class="mt-2" style="display: none">
                                            <small class="text-muted">
                                                <div><strong>黑名单:</strong> <span id="filterConfigBlacklist"></span></div>
                                                <div><strong>白名单:</strong> <span id="filterConfigWhitelist"></span></div>
                                            </small>
                                        </div>
                                    </div>

                                    <div class="mb-4">
                                        <label class="form-label">选择URL过滤配置</label>
                                        <select class="form-select" id="urlFilterConfigSelect" onchange="showUrlFilterConfigDetails(this.value)">
                                            <option value="">不使用URL过滤</option>
                                        </select>
                                        <div id="urlFilterConfigDetails" class="mt-2" style="display: none">
                                            <small class="text-muted">
                                                <div><strong>黑名单规则:</strong> <span id="urlFilterConfigBlacklist"></span></div>
                                                <div><strong>白名单域名:</strong> <span id="urlFilterConfigWhitelist"></span></div>
                                            </small>
                                        </div>
                                    </div>

                                    <div class="mb-4">
                                        <label class="form-label">搜索 URL 列表</label>
                                        <div class="row">
                                            <!-- 左侧原始文本框 -->
                                            <div class="col-md-6">
                                                <textarea class="form-control" id="urls" rows="10" placeholder="每行一个URL"></textarea>
                                            </div>
                                            <!-- 右侧解析展示 -->
                                            <div class="col-md-6">
                                                <div class="url-list-container">
                                                    <div class="table-responsive">
                                                        <table class="table table-hover table-sm">
                                                            <thead>
                                                                <tr>
                                                                    <th style="width: 40px">#</th>
                                                                    <th>搜索条件</th>
                                                                    <th style="width: 80px">操作</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody id="urlList">
                                                                <!-- 动态填充 -->
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="skipBlacklistCheck" checked />
                                            <label class="form-check-label" for="skipBlacklistCheck">过滤黑白名单</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="skipDuplicateCheck" checked />
                                            <label class="form-check-label" for="skipDuplicateCheck">过滤重复链接</label>
                                        </div>
                                    </div>

                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="skipTaskDuplicateCheck" checked />
                                        <label class="form-check-label" for="skipTaskDuplicateCheck">任务内去重（同一次爬取任务中相同链接只保留第一次出现）</label>
                                    </div>

                                    <div class="d-flex gap-2">
                                        <button class="btn btn-primary" id="startBtn" onclick="startCrawler()"><i class="iconfont icon-play"></i>开始爬取</button>
                                        <button class="btn btn-danger" id="stopBtn" onclick="stopCrawler()" style="display: none"><i class="iconfont icon-stop"></i>停止爬取</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 配置管理页面 -->
                <div class="tab-pane fade" id="configs">
                    <div class="card">
                        <div class="card-body">
                            <form id="configForm" class="mb-4" onsubmit="saveConfig(event)">
                                <div class="mb-3">
                                    <label class="form-label">配置名称</label>
                                    <input type="text" class="form-control" id="configName" required />
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">URL列表 (每行一个)</label>
                                    <textarea class="form-control" id="configUrls" rows="10" required></textarea>
                                </div>
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary"><i class="iconfont icon-save"></i>保存配置</button>
                                </div>
                            </form>

                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>名称</th>
                                            <th>URL数量</th>
                                            <th>URL列表</th>
                                            <th>创建时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="configsList"></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据管理页面 -->
                <div class="tab-pane fade" id="results">
                    <div class="card mb-4">
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <input type="text" class="form-control" id="searchTitle" placeholder="搜索标题" />
                                </div>
                                <div class="col-md-3">
                                    <input type="text" class="form-control" id="url" placeholder="结果链接" />
                                </div>
                                <div class="col-md-3">
                                    <input type="text" class="form-control" id="searchUrl" placeholder="搜索链接" />
                                </div>
                                <div class="col-md-3">
                                    <input type="text" class="form-control" id="searchExecutionKey" placeholder="执行ID" />
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">创建时间</label>
                                    <div class="input-group">
                                        <input type="datetime-local" class="form-control" id="searchStartDate" placeholder="开始时间" />
                                        <span class="input-group-text">至</span>
                                        <input type="datetime-local" class="form-control" id="searchEndDate" placeholder="结束时间" />
                                    </div>
                                </div>
                                <!-- 添加搜索按钮 -->
                                <div class="col-12">
                                    <button class="btn btn-primary" onclick="searchResults()"><i class="iconfont icon-search"></i> 搜索</button>
                                    <button class="btn btn-secondary" onclick="resetSearch()"><i class="iconfont icon-refresh"></i> 重置</button>
                                    <!-- 结果总数量 -->
                                    <span id="totalResults" style="margin-left: 10px"></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>执行ID</th>
                                            <th>标题</th>
                                            <th>结果链接</th>
                                            <th>搜索链接</th>
                                            <th>创建时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="resultsList"></tbody>
                                </table>
                                <nav>
                                    <ul class="pagination justify-content-center" id="pagination"></ul>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 爬取记录页面 -->
                <div class="tab-pane fade" id="executions">
                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>执行ID</th>
                                            <th>开始时间</th>
                                            <th>结果数量</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="executionsList"></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 文案过滤管理页面 -->
                <div class="tab-pane fade" id="filters">
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">过滤配置管理</h5>
                                    <button class="btn btn-primary" onclick="showCreateFilterConfigModal()"><i class="iconfont icon-add"></i> 新建配置</button>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table">
                                            <thead>
                                                <tr>
                                                    <th>配置名称</th>
                                                    <th>描述</th>
                                                    <th>状态</th>
                                                    <th>白名单优先</th>
                                                    <th>黑名单数量</th>
                                                    <th>白名单数量</th>
                                                    <th>创建时间</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody id="filterConfigsList">
                                                <!-- 动态填充 -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 当前选中的配置详情 -->
                    <div id="filterConfigDetail" style="display: none">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card mb-4">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0">标题黑名单</h5>
                                        <div>
                                            <button class="btn btn-sm btn-primary" onclick="showBatchAddBlacklistModal()"><i class="iconfont icon-add"></i> 批量添加</button>
                                            <button class="btn btn-sm btn-secondary" onclick="showEditConfigModal()"><i class="iconfont icon-edit"></i> 编辑配置</button>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <form id="blacklistForm" class="mb-3" onsubmit="addToConfigBlacklist(event)">
                                            <div class="input-group">
                                                <input type="text" class="form-control" id="blacklistKeyword" placeholder="输入关键词..." required />
                                                <button type="submit" class="btn btn-primary">添加</button>
                                            </div>
                                        </form>
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>关键词</th>
                                                        <th style="width: 80px">操作</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="configBlacklistTable">
                                                    <!-- 动态填充 -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card mb-4">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0">标题白名单</h5>
                                        <button class="btn btn-sm btn-primary" onclick="showBatchAddWhitelistModal()"><i class="iconfont icon-add"></i> 批量添加</button>
                                    </div>
                                    <div class="card-body">
                                        <form id="whitelistForm" class="mb-3" onsubmit="addToConfigWhitelist(event)">
                                            <div class="input-group">
                                                <input type="text" class="form-control" id="whitelistKeyword" placeholder="输入关键词..." required />
                                                <button type="submit" class="btn btn-primary">添加</button>
                                            </div>
                                        </form>
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>关键词</th>
                                                        <th style="width: 80px">操作</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="configWhitelistTable">
                                                    <!-- 动态填充 -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">过滤规则设置</h5>
                            </div>
                            <div class="card-body">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="configEnableTitleFilter" />
                                    <label class="form-check-label" for="configEnableTitleFilter">启用标题过滤</label>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="configWhitelistOverridesBlacklist" />
                                    <label class="form-check-label" for="configWhitelistOverridesBlacklist">白名单必须匹配（必须匹配白名单中的关键词才会保留）</label>
                                </div>
                                <button class="btn btn-primary" onclick="saveFilterConfigSettings()">保存设置</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- URL过滤规则配置页面 -->
                <div class="tab-pane fade" id="url-filters">
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">URL过滤配置管理</h5>
                                    <button class="btn btn-primary" onclick="showCreateUrlFilterConfigModal()"><i class="iconfont icon-add"></i> 新建配置</button>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table">
                                            <thead>
                                                <tr>
                                                    <th>配置名称</th>
                                                    <th>描述</th>
                                                    <th>状态</th>
                                                    <th>黑名单数量</th>
                                                    <th>白名单数量</th>
                                                    <th>创建时间</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody id="urlFilterConfigsList"></tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 配置详情区域 -->
                    <div id="urlFilterConfigDetail" style="display: none">
                        <div class="row">
                            <!-- URL黑名单配置 -->
                            <div class="col-md-6">
                                <div class="card mb-4">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0">URL黑名单规则</h5>
                                        <div>
                                            <button class="btn btn-sm btn-primary" onclick="showBatchAddUrlBlacklistModal()"><i class="iconfont icon-add"></i> 批量添加</button>
                                            <button class="btn btn-sm btn-secondary" onclick="showEditUrlFilterConfigModal()"><i class="iconfont icon-edit"></i> 编辑配置</button>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <form id="urlBlacklistForm" class="mb-3" onsubmit="addUrlBlacklist(event)">
                                            <div class="mb-3">
                                                <label class="form-label">URL规则生成器</label>
                                                <div class="input-group mb-2">
                                                    <input type="text" class="form-control" id="urlPatternInput" placeholder="输入URL或域名..." />
                                                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">选择匹配方式</button>
                                                    <ul class="dropdown-menu dropdown-menu-end">
                                                        <li><a class="dropdown-item" href="#" onclick="generatePattern('exact')">精确匹配</a></li>
                                                        <li><a class="dropdown-item" href="#" onclick="generatePattern('domain')">域名匹配</a></li>
                                                        <li><a class="dropdown-item" href="#" onclick="generatePattern('prefix')">前缀匹配</a></li>
                                                        <li><a class="dropdown-item" href="#" onclick="generatePattern('suffix')">后缀匹配</a></li>
                                                        <li><a class="dropdown-item" href="#" onclick="generatePattern('contains')">包含匹配</a></li>
                                                        <li><hr class="dropdown-divider" /></li>
                                                        <li><a class="dropdown-item" href="#" onclick="generatePattern('subdomains')">包含子域名</a></li>
                                                        <li><a class="dropdown-item" href="#" onclick="generatePattern('path')">路径匹配</a></li>
                                                        <li><a class="dropdown-item" href="#" onclick="generatePattern('extension')">文件扩展名匹配</a></li>
                                                        <li><a class="dropdown-item" href="#" onclick="generatePattern('parameters')">带参数匹配</a></li>
                                                    </ul>
                                                </div>
                                                <div class="form-text">
                                                    常用匹配示例：
                                                    <span class="badge bg-light text-dark me-1" onclick="setExamplePattern('example.com')">域名匹配</span>
                                                    <span class="badge bg-light text-dark me-1" onclick="setExamplePattern('https://example.com')">完整URL匹配</span>
                                                    <span class="badge bg-light text-dark me-1" onclick="setExamplePattern('*.example.com')">子域名匹配</span>
                                                    <span class="badge bg-light text-dark me-1" onclick="setExamplePattern('example.com/path/*')">路径通配符</span>
                                                </div>
                                            </div>

                                            <div class="mb-3">
                                                <label class="form-label">生成的正则表达式</label>
                                                <div class="input-group">
                                                    <input type="text" class="form-control" id="urlBlacklistPattern" readonly />
                                                    <button class="btn btn-outline-secondary" type="button" onclick="copyPattern()"><i class="iconfont icon-copy"></i> 复制</button>
                                                    <button class="btn btn-outline-secondary" type="button" onclick="editPattern()"><i class="iconfont icon-edit"></i> 编辑</button>
                                                </div>
                                                <div class="form-text">生成的正则表达式规则，可以直接使用或进行修改</div>
                                            </div>

                                            <div class="mb-3">
                                                <label class="form-label">规则测试</label>
                                                <div class="input-group">
                                                    <input type="text" class="form-control" id="testUrl" placeholder="输入URL进行测试..." />
                                                    <button class="btn btn-outline-secondary" type="button" onclick="testPattern()">测试</button>
                                                </div>
                                                <div id="testResult" class="form-text"></div>
                                            </div>
                                            <button type="submit" class="btn btn-primary">添加</button>
                                        </form>
                                        <div class="table-responsive">
                                            <table class="table">
                                                <thead>
                                                    <tr>
                                                        <th>正则表达式</th>
                                                        <th style="width: 80px">操作</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="urlBlacklistTable"></tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- URL白名单配置 -->
                            <div class="col-md-6">
                                <div class="card mb-4">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0">URL白名单规则</h5>
                                        <button class="btn btn-sm btn-primary" onclick="showBatchAddUrlWhitelistModal()"><i class="iconfont icon-add"></i> 批量添加</button>
                                    </div>
                                    <div class="card-body">
                                        <form id="urlWhitelistForm" class="mb-3" onsubmit="addUrlWhitelist(event)">
                                            <div class="mb-3">
                                                <label class="form-label">域名</label>
                                                <input type="text" class="form-control" id="urlWhitelistDomain" placeholder="输入需要允许的域名" required />
                                                <small class="form-text text-muted">输入需要允许的域名，例如: example.com</small>
                                            </div>
                                            <button type="submit" class="btn btn-primary">添加</button>
                                        </form>
                                        <div class="table-responsive">
                                            <table class="table">
                                                <thead>
                                                    <tr>
                                                        <th>域名</th>
                                                        <th style="width: 80px">操作</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="urlWhitelistTable"></tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 状态提示 -->
            <div id="status" class="alert" style="display: none; position: fixed; bottom: 20px; right: 20px"></div>
        </div>

        <!-- 创建过滤配置的模态框 -->
        <div class="modal fade" id="createFilterConfigModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">创建新的过滤配置</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form id="createFilterConfigForm">
                            <div class="mb-3">
                                <label for="configName" class="form-label">配置名称</label>
                                <input type="text" class="form-control" id="configName" required />
                            </div>
                            <div class="mb-3">
                                <label for="configDescription" class="form-label">配置描述</label>
                                <textarea class="form-control" id="configDescription" rows="3"></textarea>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="configEnabled" checked />
                                <label class="form-check-label" for="configEnabled">启用过滤</label>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="configWhitelistOverrides" checked />
                                <label class="form-check-label" for="configWhitelistOverrides">白名单优先</label>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="createFilterConfig()">创建</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 添加批量添加黑名单的模态框 -->
        <div class="modal fade" id="batchAddBlacklistModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">批量添加黑名单关键词</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form id="batchAddBlacklistForm">
                            <div class="mb-3">
                                <label for="blacklistKeywords" class="form-label">关键词列表（每行一个）</label>
                                <textarea class="form-control" id="blacklistKeywords" rows="10" required></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="batchAddToBlacklist()">添加</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 添加批量添加白名单的模态框 -->
        <div class="modal fade" id="batchAddWhitelistModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">批量添加白名单关键词</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form id="batchAddWhitelistForm">
                            <div class="mb-3">
                                <label for="whitelistKeywords" class="form-label">关键词列表（每行一个）</label>
                                <textarea class="form-control" id="whitelistKeywords" rows="10" required></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="batchAddToWhitelist()">添加</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 添加编辑配置的模态框 -->
        <div class="modal fade" id="editConfigModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">编辑配置信息</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form id="editConfigForm">
                            <div class="mb-3">
                                <label for="editConfigName" class="form-label">配置名称</label>
                                <input type="text" class="form-control" id="editConfigName" required />
                            </div>
                            <div class="mb-3">
                                <label for="editConfigDescription" class="form-label">配置描述</label>
                                <textarea class="form-control" id="editConfigDescription" rows="3"></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="updateConfigMeta()">保存</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 创建URL过滤配置的模态框 -->
        <div class="modal fade" id="createUrlFilterConfigModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">创建新的URL过滤配置</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form id="createUrlFilterConfigForm">
                            <div class="mb-3">
                                <label for="urlConfigName" class="form-label">配置名称</label>
                                <input type="text" class="form-control" id="urlConfigName" required />
                            </div>
                            <div class="mb-3">
                                <label for="urlConfigDescription" class="form-label">配置描述</label>
                                <textarea class="form-control" id="urlConfigDescription" rows="3"></textarea>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="urlConfigEnabled" checked />
                                <label class="form-check-label" for="urlConfigEnabled">启用过滤</label>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="createUrlFilterConfig()">创建</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 编辑URL过滤配置的模态框 -->
        <div class="modal fade" id="editUrlFilterConfigModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">编辑URL过滤配置</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form id="editUrlFilterConfigForm">
                            <div class="mb-3">
                                <label for="editUrlConfigName" class="form-label">配置名称</label>
                                <input type="text" class="form-control" id="editUrlConfigName" required />
                            </div>
                            <div class="mb-3">
                                <label for="editUrlConfigDescription" class="form-label">配置描述</label>
                                <textarea class="form-control" id="editUrlConfigDescription" rows="3"></textarea>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="editUrlConfigEnabled" />
                                <label class="form-check-label" for="editUrlConfigEnabled">启用过滤</label>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="updateUrlFilterConfig()">保存</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 添加批量添加URL黑名单的模态框 -->
        <div class="modal fade" id="batchAddUrlBlacklistModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">批量添加URL黑名单规则</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form id="batchAddUrlBlacklistForm">
                            <div class="mb-3">
                                <label for="urlBlacklistPatterns" class="form-label">正则表达式列表（每行一个）</label>
                                <textarea class="form-control" id="urlBlacklistPatterns" rows="10" required></textarea>
                                <small class="form-text text-muted">每行输入一个正则表达式</small>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="batchAddUrlBlacklist()">添加</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 添加批量添加URL白名单的模态框 -->
        <div class="modal fade" id="batchAddUrlWhitelistModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">批量添加URL白名单规则</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form id="batchAddUrlWhitelistForm">
                            <div class="mb-3">
                                <label for="urlWhitelistDomains" class="form-label">域名列表（每行一个）</label>
                                <textarea class="form-control" id="urlWhitelistDomains" rows="10" required></textarea>
                                <small class="form-text text-muted">每行输入一个域名</small>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="batchAddUrlWhitelist()">添加</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 在合适位置添加弹窗HTML -->
        <div class="modal fade" id="executionConfigModal" tabindex="-1" aria-labelledby="executionConfigModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="executionConfigModalLabel">执行配置详情</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <pre id="executionConfigJson" style="background: #f8f9fa; padding: 1em; border-radius: 6px; max-height: 400px; overflow: auto"></pre>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" id="reuseExecutionConfigBtn">复用此配置</button>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div>

        <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
        <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
        <script>
            let currentPage = 1
            const perPage = 20

            let searchParams = {
                title: '',
                full_title: '',
                url: '',
                executionKey: '',
                date: '',
                search_url: '',
            }

            // 全局变量
            let logPollingInterval = null
            let lastLogId = 0

            // 黑白名单数据
            let titleBlacklist = []
            let titleWhitelist = []
            let filterSettings = {
                enableTitleFilter: true,
                whitelistOverridesBlacklist: true,
            }

            // 全局变量
            let currentFilterConfigId = null
            let filterConfigs = []

            // 全局变量
            let currentUrlFilterConfigId = null

            // 加载所有过滤配置
            async function loadFilterConfigs() {
                try {
                    const response = await fetch('/api/filter-configs')
                    const configs = await response.json()
                    filterConfigs = configs

                    const tbody = document.getElementById('filterConfigsList')
                    if (!tbody) return

                    tbody.innerHTML = configs
                        .map(
                            (config) => `
                        <tr>
                            <td>${config.name}</td>
                            <td>${config.description || '-'}</td>
                            <td>${config.enabled ? '<span class="badge bg-success">启用</span>' : '<span class="badge bg-secondary">禁用</span>'}</td>
                            <td>${config.whitelist_required ? '是' : '否'}</td>
                            <td id="blacklist-count-${config.id}">加载中...</td>
                            <td id="whitelist-count-${config.id}">加载中...</td>
                            <td>${formatDate(config.created_at)}</td>
                            <td>
                                <button class="btn btn-sm btn-primary" onclick="loadFilterConfig(${config.id})">
                                    <i class="iconfont icon-edit"></i> 编辑
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="deleteFilterConfig(${config.id})">
                                    <i class="iconfont icon-delete"></i> 删除
                                </button>
                            </td>
                        </tr>
                    `
                        )
                        .join('')

                    // 更新爬虫配置页面的过滤配置选择器
                    updateFilterConfigSelect(configs)

                    showStatus('过滤配置已加载', 'success')
                } catch (error) {
                    showStatus('加载过滤配置失败: ' + error, 'error')
                }
            }

            // 加载配置的关键词数量
            async function loadFilterConfigCounts(configId) {
                try {
                    const response = await fetch(`/api/filter-config/${configId}`)
                    const config = await response.json()

                    const blacklistCount = document.getElementById(`blacklist-count-${configId}`)
                    const whitelistCount = document.getElementById(`whitelist-count-${configId}`)

                    if (blacklistCount) blacklistCount.textContent = config.blacklist.length
                    if (whitelistCount) whitelistCount.textContent = config.whitelist.length
                } catch (error) {
                    console.error('加载配置计数失败:', error)
                }
            }

            // 更新爬虫配置页面的过滤配置选择器
            function updateFilterConfigSelect(configs) {
                const select = document.getElementById('filterConfigSelect')
                if (!select) return

                select.innerHTML = `<option value="">不使用过滤</option>` + configs.map((config) => `<option value="${config.id}">${config.name}</option>`).join('')
            }

            // 加载单个过滤配置详情
            async function loadFilterConfig(configId) {
                try {
                    const response = await fetch(`/api/filter-config/${configId}`)
                    const config = await response.json()

                    currentFilterConfigId = config.id

                    // 显示配置详情区域
                    document.getElementById('filterConfigDetail').style.display = 'block'

                    // 更新设置
                    document.getElementById('configEnableTitleFilter').checked = config.enabled
                    document.getElementById('configWhitelistOverridesBlacklist').checked = config.whitelist_required

                    // 更新黑名单表格
                    updateConfigBlacklistTable(config.blacklist)

                    // 更新白名单表格
                    updateConfigWhitelistTable(config.whitelist)

                    showStatus('配置已加载', 'success')
                } catch (error) {
                    showStatus('加载配置失败: ' + error, 'error')
                }
            }

            // 更新配置黑名单表格
            function updateConfigBlacklistTable(blacklist) {
                const tbody = document.getElementById('configBlacklistTable')
                if (!tbody) return

                tbody.innerHTML = blacklist
                    .map(
                        (keyword) => `
                    <tr>
                        <td>${keyword}</td>
                        <td>
                            <button class="btn btn-sm btn-danger" onclick="removeFromConfigBlacklist('${keyword}')">
                                <i class="iconfont icon-delete"></i>删除
                            </button>
                        </td>
                    </tr>
                `
                    )
                    .join('')
            }

            // 更新配置白名单表格
            function updateConfigWhitelistTable(whitelist) {
                const tbody = document.getElementById('configWhitelistTable')
                if (!tbody) return

                tbody.innerHTML = whitelist
                    .map(
                        (keyword) => `
                    <tr>
                        <td>${keyword}</td>
                        <td>
                            <button class="btn btn-sm btn-danger" onclick="removeFromConfigWhitelist('${keyword}')">
                                <i class="iconfont icon-delete"></i>删除
                            </button>
                        </td>
                    </tr>
                `
                    )
                    .join('')
            }

            // 添加到配置黑名单
            async function addToConfigBlacklist(event) {
                event.preventDefault()

                if (!currentFilterConfigId) {
                    showStatus('请先选择一个配置', 'error')
                    return
                }

                const keyword = document.getElementById('blacklistKeyword').value.trim()
                if (!keyword) {
                    showStatus('关键词不能为空', 'error')
                    return
                }

                try {
                    const response = await fetch(`/api/filter-config/${currentFilterConfigId}/blacklist`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ keyword }),
                    })

                    const result = await response.json()
                    if (result.status === 'success') {
                        document.getElementById('blacklistKeyword').value = ''
                        loadFilterConfig(currentFilterConfigId)
                        showStatus('已添加到黑名单', 'success')
                    } else {
                        showStatus(result.message, 'error')
                    }
                } catch (error) {
                    showStatus('添加失败: ' + error, 'error')
                }
            }

            // 从配置黑名单移除
            async function removeFromConfigBlacklist(keyword) {
                if (!currentFilterConfigId) {
                    showStatus('请先选择一个配置', 'error')
                    return
                }

                try {
                    const response = await fetch(`/api/filter-config/${currentFilterConfigId}/blacklist`, {
                        method: 'DELETE',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ keyword }),
                    })

                    const result = await response.json()
                    if (result.status === 'success') {
                        loadFilterConfig(currentFilterConfigId)
                        showStatus('已从黑名单移除', 'success')
                    } else {
                        showStatus(result.message, 'error')
                    }
                } catch (error) {
                    showStatus('移除失败: ' + error, 'error')
                }
            }

            // 添加到配置白名单
            async function addToConfigWhitelist(event) {
                event.preventDefault()

                if (!currentFilterConfigId) {
                    showStatus('请先选择一个配置', 'error')
                    return
                }

                const keyword = document.getElementById('whitelistKeyword').value.trim()
                if (!keyword) {
                    showStatus('关键词不能为空', 'error')
                    return
                }

                try {
                    const response = await fetch(`/api/filter-config/${currentFilterConfigId}/whitelist`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ keyword }),
                    })

                    const result = await response.json()
                    if (result.status === 'success') {
                        document.getElementById('whitelistKeyword').value = ''
                        loadFilterConfig(currentFilterConfigId)
                        showStatus('已添加到白名单', 'success')
                    } else {
                        showStatus(result.message, 'error')
                    }
                } catch (error) {
                    showStatus('添加失败: ' + error, 'error')
                }
            }

            // 从配置白名单移除
            async function removeFromConfigWhitelist(keyword) {
                if (!currentFilterConfigId) {
                    showStatus('请先选择一个配置', 'error')
                    return
                }

                try {
                    const response = await fetch(`/api/filter-config/${currentFilterConfigId}/whitelist`, {
                        method: 'DELETE',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ keyword }),
                    })

                    const result = await response.json()
                    if (result.status === 'success') {
                        loadFilterConfig(currentFilterConfigId)
                        showStatus('已从白名单移除', 'success')
                    } else {
                        showStatus(result.message, 'error')
                    }
                } catch (error) {
                    showStatus('移除失败: ' + error, 'error')
                }
            }

            // 保存过滤配置设置
            async function saveFilterConfigSettings() {
                if (!currentFilterConfigId) {
                    showStatus('请先选择一个配置', 'error')
                    return
                }

                const enabled = document.getElementById('configEnableTitleFilter').checked
                const whitelist_required = document.getElementById('configWhitelistOverridesBlacklist').checked

                // 获取当前配置信息
                const currentConfig = filterConfigs.find((c) => c.id === currentFilterConfigId)
                if (!currentConfig) {
                    showStatus('配置不存在', 'error')
                    return
                }

                try {
                    const response = await fetch(`/api/filter-config/${currentFilterConfigId}`, {
                        method: 'PUT',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            name: currentConfig.name,
                            description: currentConfig.description,
                            enabled: enabled,
                            whitelist_required: whitelist_required,
                        }),
                    })

                    const result = await response.json()
                    if (result.status === 'success') {
                        showStatus('设置已保存', 'success')
                        loadFilterConfigs()
                    } else {
                        showStatus(result.message, 'error')
                    }
                } catch (error) {
                    showStatus('保存设置失败: ' + error, 'error')
                }
            }

            // 删除过滤配置
            async function deleteFilterConfig(configId) {
                if (!confirm('确定要删除此配置吗？此操作不可恢复。')) {
                    return
                }

                try {
                    const response = await fetch(`/api/filter-config/${configId}`, {
                        method: 'DELETE',
                    })

                    const result = await response.json()
                    if (result.status === 'success') {
                        showStatus('配置已删除', 'success')

                        // 如果删除的是当前正在编辑的配置，则隐藏详情区域
                        if (currentFilterConfigId === configId) {
                            document.getElementById('filterConfigDetail').style.display = 'none'
                            currentFilterConfigId = null
                        }

                        loadFilterConfigs()
                    } else {
                        showStatus(result.message, 'error')
                    }
                } catch (error) {
                    showStatus('删除配置失败: ' + error, 'error')
                }
            }

            // 格式化日期
            function formatDate(dateString) {
                if (!dateString) return '-'

                const date = new Date(dateString)
                if (isNaN(date.getTime())) return dateString

                return date.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                })
            }

            // 所有函数定义
            function showStatus(message, type) {
                const status = document.getElementById('status')
                status.textContent = message
                status.className = type
                status.style.display = 'block'
                setTimeout(() => {
                    status.style.display = 'none'
                }, 3000)
            }

            function formatDate(dateString) {
                const date = new Date(dateString)
                return date.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                })
            }

            async function loadSelectedConfig() {
                const select = document.getElementById('configSelect')
                const configId = select.value
                if (!configId) return

                try {
                    const response = await fetch(`/api/config/${configId}`)
                    const config = await response.json()

                    // 填充表单
                    document.getElementById('urls').value = Array.isArray(config.urls) ? config.urls.join('\n') : ''

                    // 更新 URL 列表显示
                    updateUrlList()

                    showStatus('配置已加载', 'success')
                } catch (error) {
                    showStatus('加载配置失败: ' + error, 'error')
                }
            }

            async function loadConfigs() {
                try {
                    const response = await fetch('/api/config')
                    const configs = await response.json()

                    // 更新选择框
                    const select = document.getElementById('configSelect')
                    select.innerHTML = '<option value="">请选择配置...</option>' + configs.map((config) => `<option value="${config.id}">${config.name}</option>`).join('')

                    // 更新配置列表表格
                    const tbody = document.getElementById('configsList')
                    if (tbody) {
                        tbody.innerHTML = configs
                            .map(
                                (config) => `
                            <tr>
                                <td>${config.id}</td>
                                <td>${config.name}</td>
                                <td>${Array.isArray(config.urls) ? config.urls.length : 0}</td>
                                <td class="config-urls" title="${Array.isArray(config.urls) ? config.urls.join('\n') : ''}">${Array.isArray(config.urls) ? config.urls.join(', ') : ''}</td>
                                <td>${formatDate(config.created_at)}</td>
                                <td>
                                    <button class="btn btn-sm btn-danger" onclick="deleteConfig(${config.id})">删除</button>
                                </td>
                            </tr>
                        `
                            )
                            .join('')
                    }
                } catch (error) {
                    showStatus('加载配置失败: ' + error, 'error')
                }
            }

            // 搜索结果
            async function searchResults() {
                // 禁用搜索按钮，显示加载状态
                const searchButton = document.querySelector('.btn-primary')
                searchButton.disabled = true
                searchButton.innerHTML = '<i class="iconfont icon-loading"></i> 搜索中...'

                // 更新搜索参数
                searchParams = {
                    title: document.getElementById('searchTitle').value.trim(),
                    url: document.getElementById('url').value.trim(),
                    executionKey: document.getElementById('searchExecutionKey').value.trim(),
                    start_date: document.getElementById('searchStartDate').value, // 新增开始时间
                    end_date: document.getElementById('searchEndDate').value, // 新增结束时间
                    searchUrl: document.getElementById('searchUrl').value.trim(),
                }

                // 重置到第一页并加载结果
                currentPage = 1
                await loadResults(1)
            }

            // 重置搜索
            function resetSearch() {
                // 清空搜索框
                document.getElementById('searchTitle').value = ''
                document.getElementById('url').value = ''
                document.getElementById('searchExecutionKey').value = ''
                document.getElementById('searchStartDate').value = '' // 清空开始时间
                document.getElementById('searchEndDate').value = '' // 清空结束时间
                document.getElementById('searchUrl').value = ''

                // 重新加载结果
                currentPage = 1
                loadResults(1)
            }

            // 修改现有的 loadResults 函数
            async function loadResults(page = 1) {
                try {
                    // 构建查询参数
                    const params = new URLSearchParams({
                        page: page,
                        per_page: perPage,
                    })

                    // 获取searchParams
                    const searchParams = {
                        title: document.getElementById('searchTitle').value.trim(),
                        url: document.getElementById('url').value.trim(),
                        executionKey: document.getElementById('searchExecutionKey').value.trim(),
                        start_date: document.getElementById('searchStartDate').value, // 新增开始时间
                        end_date: document.getElementById('searchEndDate').value, // 新增结束时间
                        search_url: document.getElementById('searchUrl').value.trim(),
                    }

                    // 添加搜索条件
                    if (searchParams.title) params.append('title', searchParams.title)
                    if (searchParams.url) params.append('url', searchParams.url)
                    if (searchParams.executionKey) params.append('executionKey', searchParams.executionKey)
                    if (searchParams.start_date) params.append('start_date', searchParams.start_date)
                    if (searchParams.end_date) params.append('end_date', searchParams.end_date)
                    if (searchParams.search_url) params.append('search_url', searchParams.search_url)

                    const response = await fetch(`/api/results?${params}`)
                    const data = await response.json()

                    const tbody = document.getElementById('resultsList')
                    if (!tbody) return

                    tbody.innerHTML = data.results
                        .map(
                            (result) => `
                            <tr>
                                <td>${result.id}</td>
                                <td class="execution-key">${result.execution_key}</td>
                                <td>${result.title}</td>
                                <td class="url-cell"><a href="${result.link}" target="_blank">${result.link}</a></td>
                                <td class="url-cell"><a href="${result.search_url}" target="_blank">${result.search_url}</a></td>
                                <td>${formatDate(result.created_at)}</td>
                                <td>
                                    <button class="btn btn-sm btn-danger" onclick="deleteResult(${result.id})">删除</button>
                                </td>
                            </tr>
                        `
                        )
                        .join('')

                    updatePagination(data.page, data.total_pages)

                    // 恢复搜索按钮状态
                    const searchButton = document.querySelector('.btn-primary')
                    if (searchButton) {
                        searchButton.disabled = false
                        searchButton.innerHTML = '<i class="iconfont icon-search"></i> 搜索'
                    }

                    // 更新结果总数量
                    document.getElementById('totalResults').textContent = `一共${data.total}条结果`
                } catch (error) {
                    showStatus('加载结果失败: ' + error, 'error')
                }
            }

            function updatePagination(currentPage, totalPages) {
                const pagination = document.getElementById('pagination')
                pagination.innerHTML = ''

                // 上一页
                pagination.innerHTML += `
                    <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                        <a class="page-link" href="#" onclick="loadResults(${currentPage - 1})">&laquo;</a>
                    </li>
                `

                // 页码
                for (let i = 1; i <= totalPages; i++) {
                    if (
                        i === 1 || // 第一页
                        i === totalPages || // 最后一页
                        (i >= currentPage - 2 && i <= currentPage + 2) // 当前页附近
                    ) {
                        pagination.innerHTML += `
                            <li class="page-item ${i === currentPage ? 'active' : ''}">
                                <a class="page-link" href="#" onclick="loadResults(${i})">${i}</a>
                            </li>
                        `
                    } else if ((i === currentPage - 3 && currentPage > 4) || (i === currentPage + 3 && currentPage < totalPages - 3)) {
                        pagination.innerHTML += `
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                        `
                    }
                }

                // 下一页
                pagination.innerHTML += `
                    <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                        <a class="page-link" href="#" onclick="loadResults(${currentPage + 1})">&raquo;</a>
                    </li>
                `
            }

            async function deleteConfig(id) {
                if (!confirm('确定要删除这个配置吗？')) return

                try {
                    const response = await fetch(`/api/config/${id}`, {
                        method: 'DELETE',
                    })
                    if (response.ok) {
                        showStatus('配置已删除', 'success')
                        loadConfigs()
                    }
                } catch (error) {
                    showStatus('删除配置失败: ' + error, 'error')
                }
            }

            async function deleteResult(id) {
                if (!confirm('确定要删除这条记录吗？')) return

                try {
                    const response = await fetch(`/api/result/${id}`, {
                        method: 'DELETE',
                    })
                    if (response.ok) {
                        showStatus('记录已删除', 'success')
                        loadResults(currentPage)
                    }
                } catch (error) {
                    showStatus('删除记录失败: ' + error, 'error')
                }
            }

            async function saveConfig(event) {
                // 阻止表单默认提交行为
                event.preventDefault()

                const name = document.getElementById('configName').value
                const urlsText = document.getElementById('configUrls').value
                const urls = urlsText.split('\n').filter((url) => url.trim())

                if (!name || urls.length === 0) {
                    showStatus('请填写配置名称和至少一个URL', 'error')
                    return
                }

                try {
                    const response = await fetch('/api/config', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            name: name,
                            urls: urls,
                        }),
                    })

                    const result = await response.json()
                    if (result.status === 'success') {
                        showStatus('配置已保存', 'success')
                        // 重置表单
                        document.getElementById('configForm').reset()
                        // 重新加载配置列表
                        loadConfigs()
                    } else {
                        showStatus('保存失败: ' + result.message, 'error')
                    }
                } catch (error) {
                    showStatus('保存失败: ' + error, 'error')
                }
            }

            async function pollLogs() {
                try {
                    const response = await fetch(`/api/logs?last_id=${lastLogId}`)
                    const data = await response.json()

                    const logsDiv = document.getElementById('crawlerLogs')
                    if (!logsDiv) return

                    if (data.logs && data.logs.length > 0) {
                        data.logs.forEach((log) => {
                            const logEntry = document.createElement('div')
                            logEntry.className = 'log-entry'
                            logEntry.textContent = log.message
                            logsDiv.appendChild(logEntry)
                            lastLogId = log.id + 1 // 更新最后的日志ID
                        })
                        // 滚动到底部
                        logsDiv.scrollTop = logsDiv.scrollHeight
                    }
                } catch (error) {
                    console.error('获取日志失败:', error)
                }
            }

            function startLogPolling() {
                stopLogPolling() // 先停止现有的轮询

                lastLogId = 0 // 重置日志ID
                const logsDiv = document.getElementById('crawlerLogs')
                if (!logsDiv) return

                logsDiv.innerHTML = '' // 清空日志显示

                // 开始新的轮询
                pollLogs() // 立即执行一次
                logPollingInterval = setInterval(pollLogs, 1000)
            }

            function stopLogPolling() {
                if (logPollingInterval) {
                    clearInterval(logPollingInterval)
                    logPollingInterval = null
                }
            }

            async function checkCrawlerStatus() {
                try {
                    const response = await fetch('/api/status')
                    const data = await response.json()
                    const startBtn = document.getElementById('startBtn')
                    const stopBtn = document.getElementById('stopBtn')

                    if (data.running) {
                        startBtn.style.display = 'none'
                        stopBtn.style.display = 'inline-block'
                    } else {
                        startBtn.style.display = 'inline-block'
                        stopBtn.style.display = 'none'
                    }
                } catch (error) {
                    console.error('检查状态失败:', error)
                }
            }

            async function stopCrawler() {
                try {
                    const response = await fetch('/api/stop', { method: 'POST' })
                    const result = await response.json()

                    if (result.status === 'success') {
                        showStatus(result.message, 'success')
                        document.getElementById('startBtn').style.display = 'inline-block'
                        document.getElementById('stopBtn').style.display = 'none'
                        stopLogPolling()
                    } else {
                        showStatus(result.message, 'error')
                    }
                } catch (error) {
                    showStatus('停止失败: ' + error, 'error')
                }
            }

            async function startCrawler() {
                const urls = document
                    .getElementById('urls')
                    .value.split('\n')
                    .filter((url) => url.trim())
                if (urls.length === 0) {
                    showStatus('请输入至少一个搜索URL', 'error')
                    return
                }

                try {
                    const response = await fetch('/api/start', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            urls: urls,
                            skipDuplicate: document.getElementById('skipDuplicateCheck').checked,
                            skipTaskDuplicate: document.getElementById('skipTaskDuplicateCheck').checked,
                            filterConfigId: document.getElementById('filterConfigSelect').value || null,
                            urlFilterConfigId: document.getElementById('urlFilterConfigSelect').value || null,
                        }),
                    })

                    const result = await response.json()
                    if (result.status === 'success') {
                        showStatus(result.message, 'success')
                        document.getElementById('startBtn').style.display = 'none'
                        document.getElementById('stopBtn').style.display = 'inline-block'
                        startLogPolling()
                    } else {
                        showStatus(result.message, 'error')
                    }
                } catch (error) {
                    showStatus('启动失败: ' + error, 'error')
                }
            }

            // 初始化函数
            function initializeApp() {
                loadConfigs()
                loadResults()
                loadFilterConfigs() // 加载文案过滤配置
                loadUrlFilterConfigs() // 加载URL过滤配置

                // 更新过滤配置选择器
                updateFilterConfigSelect()
                updateUrlFilterConfigSelect()

                // 标签页切换事件监听
                document.querySelectorAll('.nav-link').forEach((tab) => {
                    tab.addEventListener('shown.bs.tab', (e) => {
                        if (e.target.id === 'configs-tab') {
                            loadConfigs()
                        } else if (e.target.id === 'results-tab') {
                            loadResults()
                        } else if (e.target.id === 'executions-tab') {
                            loadExecutions()
                        } else if (e.target.id === 'filters-tab') {
                            loadFilterConfigs() // 切换到过滤器标签时重新加载
                        } else if (e.target.id === 'url-filters-tab') {
                            loadUrlFilterConfigs() // 添加这一行
                        } else if (logPollingInterval) {
                            clearInterval(logPollingInterval)
                            logPollingInterval = null
                        }
                    })
                })

                // 定期检查状态
                setInterval(checkCrawlerStatus, 2000)

                updateCrawlerConfigForm()
                loadUrlFilterConfigSelect()
            }

            // 页面加载完成后初始化
            document.addEventListener('DOMContentLoaded', initializeApp)

            // 加载爬取记录
            async function loadExecutions() {
                try {
                    const response = await fetch('/api/executions')
                    const data = await response.json()

                    const tbody = document.getElementById('executionsList')
                    tbody.innerHTML = data
                        .map(
                            (exec) => `
                        <tr>
                            <td class="execution-key">${exec.execution_key}</td>
                            <td>${formatDate(exec.created_at)}</td>
                            <td>${exec.result_count}</td>
                            <td>
                                ${
                                    exec.result_count > 0
                                        ? `<button class="btn btn-sm btn-primary" onclick="downloadResults('${exec.execution_key}')">
                                        下载CSV
                                    </button>`
                                        : '<span class="text-muted">无数据</span>'
                                }
                                <button class="btn btn-sm btn-secondary" onclick="viewExecutionConfig('${exec.execution_key}')">查看配置</button>
                                <button class="btn btn-sm btn-danger" onclick="deleteExecution('${exec.execution_key}')">
                                    删除
                                </button>
                            </td>
                        </tr>
                    `
                        )
                        .join('')
                } catch (error) {
                    showStatus('加载爬取记录失败: ' + error, 'error')
                }
            }

            // 下载结果
            async function downloadResults(executionKey) {
                try {
                    const response = await fetch(`/api/execution/${executionKey}/download`)
                    const blob = await response.blob()
                    const url = window.URL.createObjectURL(blob)
                    const a = document.createElement('a')
                    a.href = url
                    a.download = `${executionKey}_results.csv`
                    document.body.appendChild(a)
                    a.click()
                    window.URL.revokeObjectURL(url)
                    document.body.removeChild(a)
                } catch (error) {
                    showStatus('下载失败: ' + error, 'error')
                }
            }

            // 删除执行记录
            async function deleteExecution(executionKey) {
                if (!confirm('确定要删除这条执行记录吗？')) return

                try {
                    const response = await fetch(`/api/execution/${executionKey}`, {
                        method: 'DELETE',
                    })
                    const result = await response.json()
                    if (result.status === 'success') {
                        showStatus('删除成功', 'success')
                        loadExecutions()
                    }
                } catch (error) {
                    showStatus('删除失败: ' + error, 'error')
                }
            }

            function parseSearchUrl(url) {
                try {
                    const urlObj = new URL(url)
                    const params = new URLSearchParams(urlObj.search)

                    // 获取所有非空的q参数
                    let searchTypes = []
                    let conditions = []
                    for (let i = 1; i <= 6; i++) {
                        const qValue = params.get(`q${i}`)
                        if (qValue && qValue.trim()) {
                            conditions.push(qValue.trim())
                        }
                    }

                    // 提取关键词
                    const keywords = conditions.join(' ').trim()

                    return {
                        types: searchTypes,
                        conditions: conditions,
                        keywords: keywords,
                    }
                } catch (e) {
                    return {
                        types: ['未知'],
                        conditions: [],
                        keywords: url,
                    }
                }
            }

            function updateUrlList() {
                const urls = document
                    .getElementById('urls')
                    .value.split('\n')
                    .filter((url) => url.trim())
                const tbody = document.getElementById('urlList')

                tbody.innerHTML = urls
                    .map((url, index) => {
                        const parsed = parseSearchUrl(url)
                        return `
                        <tr>
                            <td>${index + 1}</td>
                            <td>
                                <div class="search-condition" title="${url}">
                                    ${parsed.conditions.map((c) => `<span class="badge bg-light text-dark me-1">${c}</span>`).join('')}
                                    <span class="search-keywords">${parsed.keywords}</span>
                                </div>
                            </td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary" onclick="window.open('${url}', '_blank')">
                                    <i class="iconfont icon-link"></i>访问
                                </button>
                            </td>
                        </tr>
                    `
                    })
                    .join('')
            }

            // 监听文本框变化
            document.getElementById('urls').addEventListener('input', updateUrlList)

            // 加载黑白名单
            async function loadFilters() {
                try {
                    const response = await fetch('/api/filters')
                    const data = await response.json()

                    titleBlacklist = data.blacklist || []
                    titleWhitelist = data.whitelist || []
                    filterSettings = data.settings || {
                        enableTitleFilter: true,
                        whitelistOverridesBlacklist: true,
                    }

                    // 更新UI
                    updateBlacklistTable()
                    updateWhitelistTable()

                    // 更新设置
                    document.getElementById('enableTitleFilter').checked = filterSettings.enableTitleFilter
                    document.getElementById('whitelistOverridesBlacklist').checked = filterSettings.whitelistOverridesBlacklist

                    showStatus('过滤规则已加载', 'success')
                } catch (error) {
                    showStatus('加载过滤规则失败: ' + error, 'error')
                }
            }

            // 更新黑名单表格
            function updateBlacklistTable() {
                const tbody = document.getElementById('blacklistTable')
                tbody.innerHTML = titleBlacklist
                    .map(
                        (keyword, index) => `
                    <tr>
                        <td>${keyword}</td>
                        <td>
                            <button class="btn btn-sm btn-danger" onclick="removeFromBlacklist(${index})">
                                <i class="iconfont icon-delete"></i>删除
                            </button>
                        </td>
                    </tr>
                `
                    )
                    .join('')
            }

            // 更新白名单表格
            function updateWhitelistTable() {
                const tbody = document.getElementById('whitelistTable')
                tbody.innerHTML = titleWhitelist
                    .map(
                        (keyword, index) => `
                    <tr>
                        <td>${keyword}</td>
                        <td>
                            <button class="btn btn-sm btn-danger" onclick="removeFromWhitelist(${index})">
                                <i class="iconfont icon-delete"></i>删除
                            </button>
                        </td>
                    </tr>
                `
                    )
                    .join('')
            }

            // 添加到黑名单
            async function addToBlacklist(event) {
                event.preventDefault()
                const keyword = document.getElementById('blacklistKeyword').value.trim()

                if (!keyword) return

                if (titleBlacklist.includes(keyword)) {
                    showStatus('该关键词已存在于黑名单中', 'error')
                    return
                }

                try {
                    const response = await fetch('/api/filters/blacklist', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ keyword }),
                    })

                    const result = await response.json()
                    if (result.status === 'success') {
                        titleBlacklist.push(keyword)
                        updateBlacklistTable()
                        document.getElementById('blacklistKeyword').value = ''
                        showStatus('已添加到黑名单', 'success')
                    } else {
                        showStatus(result.message, 'error')
                    }
                } catch (error) {
                    showStatus('添加失败: ' + error, 'error')
                }
            }

            // 添加到白名单
            async function addToWhitelist(event) {
                event.preventDefault()
                const keyword = document.getElementById('whitelistKeyword').value.trim()

                if (!keyword) return

                if (titleWhitelist.includes(keyword)) {
                    showStatus('该关键词已存在于白名单中', 'error')
                    return
                }

                try {
                    const response = await fetch('/api/filters/whitelist', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ keyword }),
                    })

                    const result = await response.json()
                    if (result.status === 'success') {
                        titleWhitelist.push(keyword)
                        updateWhitelistTable()
                        document.getElementById('whitelistKeyword').value = ''
                        showStatus('已添加到白名单', 'success')
                    } else {
                        showStatus(result.message, 'error')
                    }
                } catch (error) {
                    showStatus('添加失败: ' + error, 'error')
                }
            }

            // 从黑名单移除
            async function removeFromBlacklist(index) {
                if (index < 0 || index >= titleBlacklist.length) return

                const keyword = titleBlacklist[index]

                try {
                    const response = await fetch('/api/filters/blacklist', {
                        method: 'DELETE',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ keyword }),
                    })

                    const result = await response.json()
                    if (result.status === 'success') {
                        titleBlacklist.splice(index, 1)
                        updateBlacklistTable()
                        showStatus('已从黑名单移除', 'success')
                    } else {
                        showStatus(result.message, 'error')
                    }
                } catch (error) {
                    showStatus('移除失败: ' + error, 'error')
                }
            }

            // 从白名单移除
            async function removeFromWhitelist(index) {
                if (index < 0 || index >= titleWhitelist.length) return

                const keyword = titleWhitelist[index]

                try {
                    const response = await fetch('/api/filters/whitelist', {
                        method: 'DELETE',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ keyword }),
                    })

                    const result = await response.json()
                    if (result.status === 'success') {
                        titleWhitelist.splice(index, 1)
                        updateWhitelistTable()
                        showStatus('已从白名单移除', 'success')
                    } else {
                        showStatus(result.message, 'error')
                    }
                } catch (error) {
                    showStatus('移除失败: ' + error, 'error')
                }
            }

            // 显示创建过滤配置的模态框
            function showCreateFilterConfigModal() {
                $('#createFilterConfigModal').modal('show')
            }

            // 创建新的过滤配置
            async function createFilterConfig() {
                const form = document.getElementById('createFilterConfigForm')
                const name = form.elements['configName'].value
                const description = form.elements['configDescription'].value
                const enabled = form.elements['configEnabled'].checked
                const whitelistOverrides = form.elements['configWhitelistOverrides'].checked

                try {
                    const response = await fetch('/api/filter-config', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            name: name,
                            description: description,
                            enabled: enabled,
                            whitelist_required: whitelistOverrides,
                        }),
                    })

                    const result = await response.json()
                    if (result.status === 'success') {
                        showStatus('配置已创建', 'success')
                        loadFilterConfigs()
                        $('#createFilterConfigModal').modal('hide')
                    } else {
                        showStatus(result.message, 'error')
                    }
                } catch (error) {
                    showStatus('创建配置失败: ' + error, 'error')
                }
            }

            // 显示批量添加黑名单模态框
            function showBatchAddBlacklistModal() {
                $('#batchAddBlacklistModal').modal('show')
            }

            // 显示批量添加白名单模态框
            function showBatchAddWhitelistModal() {
                $('#batchAddWhitelistModal').modal('show')
            }

            // 显示编辑配置模态框
            function showEditConfigModal() {
                if (!currentFilterConfigId) {
                    showStatus('请先选择一个配置', 'error')
                    return
                }

                // 获取当前配置信息
                const currentConfig = filterConfigs.find((c) => c.id === parseInt(currentFilterConfigId))
                if (!currentConfig) {
                    showStatus('配置不存在', 'error')
                    return
                }

                // 填充表单
                document.getElementById('editConfigName').value = currentConfig.name
                document.getElementById('editConfigDescription').value = currentConfig.description || ''

                $('#editConfigModal').modal('show')
            }

            // 更新配置元数据
            async function updateConfigMeta() {
                if (!currentFilterConfigId) {
                    showStatus('请先选择一个配置', 'error')
                    return
                }

                const name = document.getElementById('editConfigName').value.trim()
                const description = document.getElementById('editConfigDescription').value.trim()

                if (!name) {
                    showStatus('配置名称不能为空', 'error')
                    return
                }

                try {
                    const response = await fetch(`/api/filter-config/${currentFilterConfigId}/meta`, {
                        method: 'PUT',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            name: name,
                            description: description,
                        }),
                    })

                    const result = await response.json()
                    if (result.status === 'success') {
                        showStatus('配置信息已更新', 'success')
                        // 重新加载配置列表和当前配置
                        loadFilterConfigs()
                        loadFilterConfig(currentFilterConfigId)
                        $('#editConfigModal').modal('hide')
                    } else {
                        showStatus(result.message, 'error')
                    }
                } catch (error) {
                    showStatus('更新配置失败: ' + error, 'error')
                }
            }

            // 批量添加黑名单关键词
            async function batchAddToBlacklist() {
                if (!currentFilterConfigId) {
                    showStatus('请先选择一个配置', 'error')
                    return
                }

                const keywords = document
                    .getElementById('blacklistKeywords')
                    .value.split('\n')
                    .filter((k) => k.trim())

                if (keywords.length === 0) {
                    showStatus('请输入至少一个关键词', 'error')
                    return
                }

                try {
                    const response = await fetch(`/api/filter-config/${currentFilterConfigId}/blacklist/batch`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ keywords }),
                    })

                    const result = await response.json()
                    if (result.status === 'success') {
                        showStatus(`成功添加${result.added_count}个关键词到黑名单`, 'success')
                        // 重新加载当前配置
                        loadFilterConfig(currentFilterConfigId)
                        $('#batchAddBlacklistModal').modal('hide')
                        // 清空输入框
                        document.getElementById('blacklistKeywords').value = ''
                    } else {
                        showStatus(result.message, 'error')
                    }
                } catch (error) {
                    showStatus('批量添加失败: ' + error, 'error')
                }
            }

            // 批量添加白名单关键词
            async function batchAddToWhitelist() {
                if (!currentFilterConfigId) {
                    showStatus('请先选择一个配置', 'error')
                    return
                }

                const keywords = document
                    .getElementById('whitelistKeywords')
                    .value.split('\n')
                    .filter((k) => k.trim())

                if (keywords.length === 0) {
                    showStatus('请输入至少一个关键词', 'error')
                    return
                }

                try {
                    const response = await fetch(`/api/filter-config/${currentFilterConfigId}/whitelist/batch`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ keywords }),
                    })

                    const result = await response.json()
                    if (result.status === 'success') {
                        showStatus(`成功添加${result.added_count}个关键词到白名单`, 'success')
                        // 重新加载当前配置
                        loadFilterConfig(currentFilterConfigId)
                        $('#batchAddWhitelistModal').modal('hide')
                        // 清空输入框
                        document.getElementById('whitelistKeywords').value = ''
                    } else {
                        showStatus(result.message, 'error')
                    }
                } catch (error) {
                    showStatus('批量添加失败: ' + error, 'error')
                }
            }

            // 加载URL黑白名单
            async function loadUrlFilters() {
                try {
                    // 加载黑名单
                    const blacklistResponse = await fetch('/api/url-blacklist')
                    const blacklist = await blacklistResponse.json()
                    updateUrlBlacklistTable(blacklist)

                    // 加载白名单
                    const whitelistResponse = await fetch('/api/url-whitelist')
                    const whitelist = await whitelistResponse.json()
                    updateUrlWhitelistTable(whitelist)

                    showStatus('URL过滤规则已加载', 'success')
                } catch (error) {
                    showStatus('加载URL过滤规则失败: ' + error, 'error')
                }
            }

            // 更新URL黑名单表格
            function updateUrlBlacklistTable(blacklist) {
                const tbody = document.getElementById('urlBlacklistTable')
                tbody.innerHTML = blacklist
                    .map(
                        (item) => `
                    <tr>
                        <td>${item.pattern}</td>
                        <td>
                            <button class="btn btn-sm btn-danger" onclick="deleteUrlBlacklist(${item.id})">
                                <i class="iconfont icon-delete"></i>删除
                            </button>
                        </td>
                    </tr>
                `
                    )
                    .join('')
            }

            // 更新URL白名单表格
            function updateUrlWhitelistTable(whitelist) {
                const tbody = document.getElementById('urlWhitelistTable')
                tbody.innerHTML = whitelist
                    .map(
                        (item) => `
                    <tr>
                        <td>${item.domain}</td>
                        <td>
                            <button class="btn btn-sm btn-danger" onclick="deleteUrlWhitelist(${item.id})">
                                <i class="iconfont icon-delete"></i>删除
                            </button>
                        </td>
                    </tr>
                `
                    )
                    .join('')
            }

            // 添加URL黑名单
            async function addUrlBlacklist(event) {
                event.preventDefault()

                if (!currentUrlFilterConfigId) {
                    showStatus('请先选择一个配置', 'error')
                    return
                }

                const pattern = document.getElementById('urlBlacklistPattern').value.trim()

                try {
                    const response = await fetch(`/api/url-filter-config/${currentUrlFilterConfigId}/blacklist`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ pattern }),
                    })

                    const result = await response.json()
                    if (result.status === 'success') {
                        document.getElementById('urlBlacklistForm').reset()
                        loadUrlFilterConfig(currentUrlFilterConfigId)
                        showStatus('已添加到URL黑名单', 'success')
                    } else {
                        showStatus(result.message, 'error')
                    }
                } catch (error) {
                    showStatus('添加失败: ' + error, 'error')
                }
            }

            // 修改添加URL白名单的函数
            async function addUrlWhitelist(event) {
                event.preventDefault()

                if (!currentUrlFilterConfigId) {
                    showStatus('请先选择一个配置', 'error')
                    return
                }

                const domain = document.getElementById('urlWhitelistDomain').value.trim()
                if (!domain) {
                    showStatus('域名不能为空', 'error')
                    return
                }

                try {
                    // 修改为正确的API路径
                    const response = await fetch('/api/url-whitelist', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            domain,
                            config_id: currentUrlFilterConfigId, // 添加 config_id 到请求体
                        }),
                    })

                    const result = await response.json()
                    if (result.status === 'success') {
                        document.getElementById('urlWhitelistForm').reset()
                        loadUrlFilterConfig(currentUrlFilterConfigId)
                        showStatus('已添加到URL白名单', 'success')
                    } else {
                        showStatus(result.message, 'error')
                    }
                } catch (error) {
                    showStatus('添加失败: ' + error, 'error')
                }
            }

            // 删除URL黑名单
            async function deleteUrlBlacklist(id) {
                if (!confirm('确定要删除这条黑名单规则吗？')) return

                try {
                    const response = await fetch(`/api/url-blacklist/${id}`, {
                        method: 'DELETE',
                    })

                    const result = await response.json()
                    if (result.status === 'success') {
                        loadUrlFilters()
                        showStatus('已删除黑名单规则', 'success')
                    } else {
                        showStatus(result.message, 'error')
                    }
                } catch (error) {
                    showStatus('删除失败: ' + error, 'error')
                }
            }

            // 修改删除URL白名单的函数
            async function deleteUrlWhitelist(id) {
                if (!confirm('确定要删除这条白名单规则吗？')) return

                try {
                    // 使用正确的API路径
                    const response = await fetch(`/api/url-filter-config/${currentUrlFilterConfigId}/whitelist/${id}`, {
                        method: 'DELETE',
                        headers: { 'Content-Type': 'application/json' },
                    })

                    const result = await response.json()
                    if (result.status === 'success') {
                        loadUrlFilterConfig(currentUrlFilterConfigId)
                        showStatus('已删除白名单规则', 'success')
                    } else {
                        showStatus(result.message, 'error')
                    }
                } catch (error) {
                    showStatus('删除失败: ' + error, 'error')
                }
            }

            // 显示创建URL过滤配置的模态框
            function showCreateUrlFilterConfigModal() {
                $('#createUrlFilterConfigModal').modal('show')
            }

            // 显示编辑URL过滤配置的模态框
            function showEditUrlFilterConfigModal() {
                if (!currentUrlFilterConfigId) {
                    showStatus('请先选择一个配置', 'error')
                    return
                }

                // 获取当前配置信息
                fetch(`/api/url-filter-config/${currentUrlFilterConfigId}`)
                    .then((response) => response.json())
                    .then((config) => {
                        document.getElementById('editUrlConfigName').value = config.name
                        document.getElementById('editUrlConfigDescription').value = config.description || ''
                        document.getElementById('editUrlConfigEnabled').checked = config.enabled
                        $('#editUrlFilterConfigModal').modal('show')
                    })
                    .catch((error) => showStatus('加载配置信息失败: ' + error, 'error'))
            }

            // 创建新的URL过滤配置
            async function createUrlFilterConfig() {
                const name = document.getElementById('urlConfigName').value.trim()
                const description = document.getElementById('urlConfigDescription').value.trim()
                const enabled = document.getElementById('urlConfigEnabled').checked

                if (!name) {
                    showStatus('配置名称不能为空', 'error')
                    return
                }

                try {
                    const response = await fetch('/api/url-filter-config', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ name, description, enabled }),
                    })

                    const result = await response.json()
                    if (result.status === 'success') {
                        showStatus('配置已创建', 'success')
                        $('#createUrlFilterConfigModal').modal('hide')
                        document.getElementById('createUrlFilterConfigForm').reset()
                        loadUrlFilterConfigs()
                    } else {
                        showStatus(result.message, 'error')
                    }
                } catch (error) {
                    showStatus('创建配置失败: ' + error, 'error')
                }
            }

            // 更新URL过滤配置
            async function updateUrlFilterConfig() {
                if (!currentUrlFilterConfigId) {
                    showStatus('请先选择一个配置', 'error')
                    return
                }

                const name = document.getElementById('editUrlConfigName').value.trim()
                const description = document.getElementById('editUrlConfigDescription').value.trim()
                const enabled = document.getElementById('editUrlConfigEnabled').checked

                if (!name) {
                    showStatus('配置名称不能为空', 'error')
                    return
                }

                try {
                    const response = await fetch(`/api/url-filter-config/${currentUrlFilterConfigId}`, {
                        method: 'PUT',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ name, description, enabled }),
                    })

                    const result = await response.json()
                    if (result.status === 'success') {
                        showStatus('配置已更新', 'success')
                        $('#editUrlFilterConfigModal').modal('hide')
                        loadUrlFilterConfigs()
                        loadUrlFilterConfig(currentUrlFilterConfigId)
                    } else {
                        showStatus(result.message, 'error')
                    }
                } catch (error) {
                    showStatus('更新配置失败: ' + error, 'error')
                }
            }

            // 加载URL过滤配置列表
            async function loadUrlFilterConfigs() {
                try {
                    const response = await fetch('/api/url-filter-configs')
                    const configs = await response.json()

                    const tbody = document.getElementById('urlFilterConfigsList')
                    tbody.innerHTML = configs
                        .map(
                            (config) => `
                        <tr>
                            <td>${config.name}</td>
                            <td>${config.description || '-'}</td>
                            <td>${config.enabled ? '<span class="badge bg-success">启用</span>' : '<span class="badge bg-secondary">禁用</span>'}</td>
                            <td id="url-blacklist-count-${config.id}">-</td>
                            <td id="url-whitelist-count-${config.id}">-</td>
                            <td>${formatDate(config.created_at)}</td>
                            <td>
                                <button class="btn btn-sm btn-primary" onclick="loadUrlFilterConfig(${config.id})">
                                    <i class="iconfont icon-edit"></i> 编辑
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="deleteUrlFilterConfig(${config.id})">
                                    <i class="iconfont icon-delete"></i> 删除
                                </button>
                            </td>
                        </tr>
                    `
                        )
                        .join('')

                    // 更新每个配置的黑白名单数量
                    configs.forEach((config) => {
                        loadUrlFilterConfigCounts(config.id)
                    })

                    showStatus('URL过滤配置已加载', 'success')
                } catch (error) {
                    showStatus('加载URL过滤配置失败: ' + error, 'error')
                }
            }

            // 加载单个URL过滤配置详情
            async function loadUrlFilterConfig(configId) {
                try {
                    const response = await fetch(`/api/url-filter-config/${configId}`)
                    const config = await response.json()

                    currentUrlFilterConfigId = config.id

                    // 显示配置详情区域
                    document.getElementById('urlFilterConfigDetail').style.display = 'block'

                    // 更新黑名单表格
                    updateUrlBlacklistTable(config.blacklist)

                    // 更新白名单表格
                    updateUrlWhitelistTable(config.whitelist)

                    showStatus('配置已加载', 'success')
                } catch (error) {
                    showStatus('加载配置失败: ' + error, 'error')
                }
            }

            // 删除URL过滤配置
            async function deleteUrlFilterConfig(configId) {
                if (!confirm('确定要删除此配置吗？此操作不可恢复。')) {
                    return
                }

                try {
                    const response = await fetch(`/api/url-filter-config/${configId}`, {
                        method: 'DELETE',
                    })

                    const result = await response.json()
                    if (result.status === 'success') {
                        showStatus('配置已删除', 'success')

                        // 如果删除的是当前正在编辑的配置，则隐藏详情区域
                        if (currentUrlFilterConfigId === configId) {
                            document.getElementById('urlFilterConfigDetail').style.display = 'none'
                            currentUrlFilterConfigId = null
                        }

                        loadUrlFilterConfigs()
                    } else {
                        showStatus(result.message, 'error')
                    }
                } catch (error) {
                    showStatus('删除配置失败: ' + error, 'error')
                }
            }

            // 修改loadUrlFilterConfigCounts函数
            async function loadUrlFilterConfigCounts(configId) {
                try {
                    const response = await fetch(`/api/url-filter-config/${configId}`)
                    const config = await response.json()

                    const blacklistCount = document.getElementById(`url-blacklist-count-${configId}`)
                    const whitelistCount = document.getElementById(`url-whitelist-count-${configId}`)

                    // 添加空值检查
                    if (blacklistCount) {
                        blacklistCount.textContent = config && config.blacklist ? config.blacklist.length : 0
                    }
                    if (whitelistCount) {
                        whitelistCount.textContent = config && config.whitelist ? config.whitelist.length : 0
                    }
                } catch (error) {
                    console.error('加载配置计数失败:', error)
                    // 出错时显示 0
                    const blacklistCount = document.getElementById(`url-blacklist-count-${configId}`)
                    const whitelistCount = document.getElementById(`url-whitelist-count-${configId}`)
                    if (blacklistCount) blacklistCount.textContent = '0'
                    if (whitelistCount) whitelistCount.textContent = '0'
                }
            }

            // 显示批量添加URL黑名单模态框
            function showBatchAddUrlBlacklistModal() {
                if (!currentUrlFilterConfigId) {
                    showStatus('请先选择一个配置', 'error')
                    return
                }
                $('#batchAddUrlBlacklistModal').modal('show')
            }

            // 显示批量添加URL白名单模态框
            function showBatchAddUrlWhitelistModal() {
                if (!currentUrlFilterConfigId) {
                    showStatus('请先选择一个配置', 'error')
                    return
                }
                $('#batchAddUrlWhitelistModal').modal('show')
            }

            // 批量添加URL黑名单规则
            async function batchAddUrlBlacklist() {
                if (!currentUrlFilterConfigId) {
                    showStatus('请先选择一个配置', 'error')
                    return
                }

                const patterns = document
                    .getElementById('urlBlacklistPatterns')
                    .value.split('\n')
                    .filter((p) => p.trim())

                if (patterns.length === 0) {
                    showStatus('请输入至少一个正则表达式', 'error')
                    return
                }

                try {
                    // 验证所有正则表达式
                    for (const pattern of patterns) {
                        try {
                            new RegExp(pattern)
                        } catch (e) {
                            showStatus(`无效的正则表达式: ${pattern}`, 'error')
                            return
                        }
                    }

                    const response = await fetch(`/api/url-filter-config/${currentUrlFilterConfigId}/blacklist/batch`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ patterns }),
                    })

                    const result = await response.json()
                    if (result.status === 'success') {
                        showStatus(`成功添加${result.added_count}个规则到黑名单`, 'success')
                        loadUrlFilterConfig(currentUrlFilterConfigId)
                        $('#batchAddUrlBlacklistModal').modal('hide')
                        document.getElementById('urlBlacklistPatterns').value = ''
                    } else {
                        showStatus(result.message, 'error')
                    }
                } catch (error) {
                    showStatus('批量添加失败: ' + error, 'error')
                }
            }

            // 批量添加URL白名单规则
            async function batchAddUrlWhitelist() {
                if (!currentUrlFilterConfigId) {
                    showStatus('请先选择一个配置', 'error')
                    return
                }

                const domains = document
                    .getElementById('urlWhitelistDomains')
                    .value.split('\n')
                    .filter((d) => d.trim())

                if (domains.length === 0) {
                    showStatus('请输入至少一个域名', 'error')
                    return
                }

                try {
                    const response = await fetch(`/api/url-filter-config/${currentUrlFilterConfigId}/whitelist/batch`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ domains }),
                    })

                    const result = await response.json()
                    if (result.status === 'success') {
                        showStatus(`成功添加${result.added_count}个域名到白名单`, 'success')
                        loadUrlFilterConfig(currentUrlFilterConfigId)
                        $('#batchAddUrlWhitelistModal').modal('hide')
                        document.getElementById('urlWhitelistDomains').value = ''
                    } else {
                        showStatus(result.message, 'error')
                    }
                } catch (error) {
                    showStatus('批量添加失败: ' + error, 'error')
                }
            }

            // 修改爬虫配置页面,添加URL过滤配置选择器
            function updateCrawlerConfigForm() {
                // 在爬虫配置页面添加URL过滤配置选择
                const configSection = document.querySelector('#crawler .card-body')
                const urlFilterSelect = `
                    <div class="mb-4">
                        <label class="form-label">选择URL过滤配置</label>
                        <select class="form-select" id="urlFilterConfigSelect">
                            <option value="">不使用URL过滤</option>
                        </select>
                        <small class="form-text text-muted">选择要应用的URL过滤规则</small>
                    </div>
                `

                // 移除原有的黑白名单选项
                const blacklistCheck = document.getElementById('skipBlacklistCheck')
                if (blacklistCheck) {
                    blacklistCheck.parentElement.remove()
                }
            }

            // 加载URL过滤配置到选择器
            async function loadUrlFilterConfigSelect() {
                try {
                    const response = await fetch('/api/url-filter-configs')
                    const configs = await response.json()

                    const select = document.getElementById('urlFilterConfigSelect')
                    if (select) {
                        select.innerHTML = `
                            <option value="">不使用URL过滤</option>
                            ${configs
                                .map(
                                    (config) => `
                                <option value="${config.id}">${config.name}</option>
                            `
                                )
                                .join('')}
                        `
                    }
                } catch (error) {
                    console.error('加载URL过滤配置失败:', error)
                }
            }

            // 更新文案过滤配置选择器
            async function updateFilterConfigSelect() {
                try {
                    const response = await fetch('/api/filter-configs')
                    const configs = await response.json()

                    const select = document.getElementById('filterConfigSelect')
                    if (select) {
                        select.innerHTML = `
                            <option value="">不使用文案过滤</option>
                            ${configs
                                .map(
                                    (config) => `
                                <option value="${config.id}">${config.name}</option>
                            `
                                )
                                .join('')}
                        `
                    }
                } catch (error) {
                    console.error('加载文案过滤配置失败:', error)
                }
            }

            // 更新URL过滤配置选择器
            async function updateUrlFilterConfigSelect() {
                try {
                    const response = await fetch('/api/url-filter-configs')
                    const configs = await response.json()

                    const select = document.getElementById('urlFilterConfigSelect')
                    if (select) {
                        select.innerHTML = `
                            <option value="">不使用URL过滤</option>
                            ${configs
                                .map(
                                    (config) => `
                                <option value="${config.id}">${config.name}</option>
                            `
                                )
                                .join('')}
                        `
                    }
                } catch (error) {
                    console.error('加载URL过滤配置失败:', error)
                }
            }

            // 显示文案过滤配置详情
            async function showFilterConfigDetails(configId) {
                const detailsDiv = document.getElementById('filterConfigDetails')
                if (!configId) {
                    detailsDiv.style.display = 'none'
                    return
                }

                try {
                    const response = await fetch(`/api/filter-config/${configId}`)
                    const config = await response.json()

                    if (config) {
                        document.getElementById('filterConfigBlacklist').textContent = Array.isArray(config.blacklist) ? config.blacklist.join(', ') : ''
                        document.getElementById('filterConfigWhitelist').textContent = Array.isArray(config.whitelist) ? config.whitelist.join(', ') : ''
                        detailsDiv.style.display = 'block'
                    } else {
                        detailsDiv.style.display = 'none'
                    }
                } catch (error) {
                    console.error('加载过滤配置详情失败:', error)
                    showStatus('加载过滤配置详情失败', 'error')
                    detailsDiv.style.display = 'none'
                }
            }

            // 显示URL过滤配置详情
            async function showUrlFilterConfigDetails(configId) {
                const detailsDiv = document.getElementById('urlFilterConfigDetails')
                if (!configId) {
                    detailsDiv.style.display = 'none'
                    return
                }

                try {
                    const response = await fetch(`/api/url-filter-config/${configId}`)
                    const config = await response.json()

                    if (config) {
                        document.getElementById('urlFilterConfigBlacklist').textContent = config.blacklist ? config.blacklist.map((item) => item.pattern).join(', ') : ''
                        document.getElementById('urlFilterConfigWhitelist').textContent = config.whitelist ? config.whitelist.map((item) => item.domain).join(', ') : ''
                        detailsDiv.style.display = 'block'
                    } else {
                        detailsDiv.style.display = 'none'
                    }
                } catch (error) {
                    console.error('加载URL过滤配置详情失败:', error)
                    showStatus('加载URL过滤配置详情失败', 'error')
                    detailsDiv.style.display = 'none'
                }
            }

            // 生成正则表达式模式
            function generatePattern(type) {
                const input = document.getElementById('urlPatternInput').value.trim()
                let pattern = ''

                switch (type) {
                    case 'exact':
                        // 精确匹配
                        pattern = `^${escapeRegExp(input)}$`
                        break
                    case 'domain':
                        // 域名匹配（包含 http/https）
                        pattern = `^https?:\\/\\/${escapeRegExp(input)}`
                        break
                    case 'prefix':
                        // 前缀匹配
                        pattern = `^${escapeRegExp(input)}`
                        break
                    case 'suffix':
                        // 后缀匹配
                        pattern = `${escapeRegExp(input)}$`
                        break
                    case 'contains':
                        // 包含匹配
                        pattern = escapeRegExp(input)
                        break
                    case 'subdomains':
                        // 子域名匹配
                        pattern = `^https?:\\/\\/([a-zA-Z0-9-]+\\.)*${escapeRegExp(input)}`
                        break
                    case 'path':
                        // 路径匹配
                        pattern = `^https?:\\/\\/${escapeRegExp(input)}\\/.*`
                        break
                    case 'extension':
                        // 文件扩展名匹配
                        pattern = `\\.${escapeRegExp(input)}$`
                        break
                    case 'parameters':
                        // 带参数匹配
                        pattern = `^${escapeRegExp(input)}\\?.*`
                        break
                }

                document.getElementById('urlBlacklistPattern').value = pattern
                updateTestResult()
            }

            // 转义正则表达式特殊字符
            function escapeRegExp(string) {
                return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
            }

            // 设置示例模式
            function setExamplePattern(example) {
                document.getElementById('urlPatternInput').value = example
                generatePattern('domain')
            }

            // 复制生成的模式
            function copyPattern() {
                const pattern = document.getElementById('urlBlacklistPattern')
                pattern.select()
                document.execCommand('copy')
                showStatus('正则表达式已复制到剪贴板', 'success')
            }

            // 启用编辑模式
            function editPattern() {
                const pattern = document.getElementById('urlBlacklistPattern')
                pattern.readOnly = !pattern.readOnly
                if (!pattern.readOnly) {
                    pattern.focus()
                }
            }

            // 测试正则表达式
            function testPattern() {
                const pattern = document.getElementById('urlBlacklistPattern').value
                const testUrl = document.getElementById('testUrl').value
                const testResult = document.getElementById('testResult')

                try {
                    const regex = new RegExp(pattern)
                    const matches = regex.test(testUrl)

                    if (matches) {
                        testResult.innerHTML = '<span class="text-success">✓ URL匹配成功</span>'
                    } else {
                        testResult.innerHTML = '<span class="text-danger">✗ URL不匹配</span>'
                    }
                } catch (e) {
                    testResult.innerHTML = `<span class="text-danger">正则表达式错误: ${e.message}</span>`
                }
            }

            // 更新测试结果
            function updateTestResult() {
                const testUrl = document.getElementById('testUrl').value
                if (testUrl) {
                    testPattern()
                }
            }

            // 添加事件监听器
            document.addEventListener('DOMContentLoaded', function () {
                // 监听URL输入变化
                document.getElementById('urlPatternInput').addEventListener('input', function () {
                    const pattern = document.getElementById('urlBlacklistPattern').value
                    if (pattern) {
                        updateTestResult()
                    }
                })

                // 监听测试URL输入变化
                document.getElementById('testUrl').addEventListener('input', function () {
                    const pattern = document.getElementById('urlBlacklistPattern').value
                    if (pattern) {
                        updateTestResult()
                    }
                })
            })

            async function viewExecutionConfig(executionKey) {
                try {
                    const response = await fetch(`/api/execution/${executionKey}/config`)
                    if (!response.ok) throw new Error('未找到配置快照')
                    const config = await response.json()
                    document.getElementById('executionConfigJson').textContent = JSON.stringify(config, null, 2)
                    // 记录当前配置以便复用
                    window._lastExecutionConfig = config
                    // 显示弹窗
                    const modal = new bootstrap.Modal(document.getElementById('executionConfigModal'))
                    modal.show()
                    // 注册复用按钮事件
                    document.getElementById('reuseExecutionConfigBtn').onclick = function () {
                        if (!window._lastExecutionConfig) return
                        const config = window._lastExecutionConfig

                        // 1. 填充urls
                        if (config.urls) {
                            document.getElementById('urls').value = Array.isArray(config.urls) ? config.urls.join('\n') : config.urls
                            updateUrlList()
                        }
                        // 2. 填充name
                        if (config.name) {
                            document.getElementById('configName').value = config.name
                        }
                        // 3. 设置文案过滤配置
                        if (config.filterConfigId !== undefined && config.filterConfigId !== null) {
                            document.getElementById('filterConfigSelect').value = config.filterConfigId
                            showFilterConfigDetails(config.filterConfigId)
                        } else {
                            document.getElementById('filterConfigSelect').value = ''
                            showFilterConfigDetails('')
                        }
                        // 4. 设置URL过滤配置
                        if (config.urlFilterConfigId !== undefined && config.urlFilterConfigId !== null) {
                            document.getElementById('urlFilterConfigSelect').value = config.urlFilterConfigId
                            showUrlFilterConfigDetails(config.urlFilterConfigId)
                        } else {
                            document.getElementById('urlFilterConfigSelect').value = ''
                            showUrlFilterConfigDetails('')
                        }
                        // 5. 设置去重选项
                        if (typeof config.skipDuplicate === 'boolean') {
                            document.getElementById('skipDuplicateCheck').checked = config.skipDuplicate
                        }
                        if (typeof config.skipTaskDuplicate === 'boolean') {
                            document.getElementById('skipTaskDuplicateCheck').checked = config.skipTaskDuplicate
                        }
                        // 6. 自动切换到"爬虫配置"标签页
                        const crawlerTab = document.getElementById('crawler-tab')
                        if (crawlerTab) {
                            new bootstrap.Tab(crawlerTab).show()
                        }
                        // 关闭弹窗
                        const modal = bootstrap.Modal.getInstance(document.getElementById('executionConfigModal'))
                        if (modal) modal.hide()
                        showStatus('已复用该执行的配置，请检查后启动', 'success')
                    }
                } catch (e) {
                    alert('加载配置失败: ' + e)
                }
            }
        </script>
    </body>
</html>
